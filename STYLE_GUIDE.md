# FME Start - Style Guide & Theme System

This style guide documents the design system and theme implementation for the FME Start project, built with shadcn/ui, Tailwind CSS, and React.

## Color Palette

### Brand Colors

Our primary brand colors are derived from the existing application:

```css
/* Header Navigation */
#032741 - Dark Blue (Navigation background)
#0a4a6b - Light variant (hover states)
#021a2d - Dark variant

/* Primary Actions */
#008544 - Green (Button color, primary actions)
#00a555 - Light variant (hover states)
#006d37 - Dark variant
```

### HSL Values (for CSS Custom Properties)

```css
/* Navigation */
--brand-nav: 202 88% 13%;      /* #032741 */
--brand-nav-hover: 202 88% 20%; /* Hover state */

/* Buttons & Primary Actions */
--brand-button: 145 100% 26%;    /* #008544 */
--brand-button-hover: 145 100% 32%; /* Hover state */
```

## Theme System

### Light Mode
- **Background**: Clean white (#ffffff)
- **Text**: Dark gray for optimal readability
- **Primary**: Brand green (#008544)
- **Secondary**: Brand navy (#032741)

### Dark Mode
- **Background**: Rich dark blue-gray
- **Text**: Light gray for comfort
- **Primary**: Lighter green for better contrast
- **Secondary**: Lighter navy for visibility

## Usage Guidelines

### Navigation Components
Use the brand navy (`bg-brand-nav`) for:
- Header navigation bars
- Main navigation elements
- Secondary navigation areas

```tsx
<nav className="bg-brand-nav text-white">
  <div className="hover:bg-brand-nav-hover">Navigation Item</div>
</nav>
```

### Buttons & Actions
Use the brand green (`bg-brand-button`) for:
- Primary action buttons
- Call-to-action elements
- Submit buttons
- Important interactive elements

```tsx
<button className="bg-brand-button hover:bg-brand-button-hover text-white">
  Primary Action
</button>
```

### Component Styling
Use shadcn theme variables for:
- Cards and containers: `bg-card`
- Text content: `text-foreground`
- Borders: `border-border`
- Inputs: `bg-input`

## Custom Utility Classes

We've added brand-specific utilities:

```css
/* Background colors */
.bg-brand-nav
.bg-brand-nav-hover
.bg-brand-button
.bg-brand-button-hover

/* Text colors */
.text-brand-nav
.text-brand-button

/* Border colors */
.border-brand-nav
.border-brand-button
```

## Accessibility

### Focus States
All interactive elements include proper focus indicators:
```css
.focus-visible-ring        /* Standard ring */
.focus-visible-ring-brand  /* Brand-colored ring */
```

### Color Contrast
- All text meets WCAG AA standards
- Brand colors have been tested for accessibility
- Dark mode provides comfortable low-light usage

## Theme Implementation

### React Context
```tsx
import { ThemeProvider, useTheme } from '@/contexts/theme-context'

// Wrap your app
<ThemeProvider>
  <App />
</ThemeProvider>

// Use in components
const { theme, setTheme, effectiveTheme } = useTheme()
```

### Theme Toggle Component
```tsx
import { ThemeToggle } from '@/components/ui/theme-toggle'

<ThemeToggle />  // Button toggle
<ThemeDropdown /> // Dropdown selection
```

## File Structure

```
src/
├── styles.css              # Main CSS with theme variables
├── lib/utils.ts            # Utility functions
├── contexts/
│   └── theme-context.tsx   # Theme context and provider
└── components/ui/
    └── theme-toggle.tsx    # Theme toggle components
```

## CSS Variables Reference

### Brand Variables
```css
:root {
  --brand-nav: 202 88% 13%;
  --brand-nav-hover: 202 88% 20%;
  --brand-button: 145 100% 26%;
  --brand-button-hover: 145 100% 32%;
}

.dark {
  --brand-nav: 202 88% 25%;
  --brand-nav-hover: 202 88% 35%;
  --brand-button: 145 100% 35%;
  --brand-button-hover: 145 100% 42%;
}
```

### Shadcn Variables
All standard shadcn/ui variables are supported:
- `--background`, `--foreground`
- `--primary`, `--primary-foreground`
- `--secondary`, `--secondary-foreground`
- `--card`, `--popover`, `--muted`
- `--accent`, `--destructive`
- `--border`, `--input`, `--ring`

## Best Practices

1. **Consistency**: Always use theme variables rather than hardcoded colors
2. **Accessibility**: Test color combinations in both light and dark modes
3. **Performance**: Theme switching includes smooth transitions
4. **Semantic Usage**: Use primary colors for actions, secondary for navigation
5. **Testing**: Verify theme behavior across different devices and preferences

## Component Examples

See the sample components in `/src/components/examples/` for practical implementations showcasing the theme system in action.
