FROM node:20-alpine

WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm install

# Copy source code
COPY . .

EXPOSE 3000

# Default command → Dev (hot reload)
CMD ["npm", "run", "dev"]

# --- For production later ---
# 1. Replace CMD with: CMD ["npm", "start"]
# 2. Or override in compose.yml: command: npm start
# 3. Optionally switch npm install to multi-stage with npm ci --only=production
