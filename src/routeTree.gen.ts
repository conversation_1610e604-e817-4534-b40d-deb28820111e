/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createServerRootRoute } from '@tanstack/react-start/server'

import { Route as rootRouteImport } from './routes/__root'
import { Route as ThemeDemoRouteImport } from './routes/theme-demo'
import { Route as DashboardTestRouteImport } from './routes/dashboard-test'
import { Route as DashboardRouteImport } from './routes/dashboard'
import { Route as AuthRouteImport } from './routes/auth'
import { Route as IndexRouteImport } from './routes/index'
import { Route as DemoStartServerFuncsRouteImport } from './routes/demo.start.server-funcs'
import { Route as DemoStartApiRequestRouteImport } from './routes/demo.start.api-request'
import { ServerRoute as ApiDemoNamesServerRouteImport } from './routes/api.demo-names'

const rootServerRouteImport = createServerRootRoute()

const ThemeDemoRoute = ThemeDemoRouteImport.update({
  id: '/theme-demo',
  path: '/theme-demo',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardTestRoute = DashboardTestRouteImport.update({
  id: '/dashboard-test',
  path: '/dashboard-test',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardRoute = DashboardRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthRoute = AuthRouteImport.update({
  id: '/auth',
  path: '/auth',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const DemoStartServerFuncsRoute = DemoStartServerFuncsRouteImport.update({
  id: '/demo/start/server-funcs',
  path: '/demo/start/server-funcs',
  getParentRoute: () => rootRouteImport,
} as any)
const DemoStartApiRequestRoute = DemoStartApiRequestRouteImport.update({
  id: '/demo/start/api-request',
  path: '/demo/start/api-request',
  getParentRoute: () => rootRouteImport,
} as any)
const ApiDemoNamesServerRoute = ApiDemoNamesServerRouteImport.update({
  id: '/api/demo-names',
  path: '/api/demo-names',
  getParentRoute: () => rootServerRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/auth': typeof AuthRoute
  '/dashboard': typeof DashboardRoute
  '/dashboard-test': typeof DashboardTestRoute
  '/theme-demo': typeof ThemeDemoRoute
  '/demo/start/api-request': typeof DemoStartApiRequestRoute
  '/demo/start/server-funcs': typeof DemoStartServerFuncsRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/auth': typeof AuthRoute
  '/dashboard': typeof DashboardRoute
  '/dashboard-test': typeof DashboardTestRoute
  '/theme-demo': typeof ThemeDemoRoute
  '/demo/start/api-request': typeof DemoStartApiRequestRoute
  '/demo/start/server-funcs': typeof DemoStartServerFuncsRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/auth': typeof AuthRoute
  '/dashboard': typeof DashboardRoute
  '/dashboard-test': typeof DashboardTestRoute
  '/theme-demo': typeof ThemeDemoRoute
  '/demo/start/api-request': typeof DemoStartApiRequestRoute
  '/demo/start/server-funcs': typeof DemoStartServerFuncsRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/auth'
    | '/dashboard'
    | '/dashboard-test'
    | '/theme-demo'
    | '/demo/start/api-request'
    | '/demo/start/server-funcs'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/auth'
    | '/dashboard'
    | '/dashboard-test'
    | '/theme-demo'
    | '/demo/start/api-request'
    | '/demo/start/server-funcs'
  id:
    | '__root__'
    | '/'
    | '/auth'
    | '/dashboard'
    | '/dashboard-test'
    | '/theme-demo'
    | '/demo/start/api-request'
    | '/demo/start/server-funcs'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AuthRoute: typeof AuthRoute
  DashboardRoute: typeof DashboardRoute
  DashboardTestRoute: typeof DashboardTestRoute
  ThemeDemoRoute: typeof ThemeDemoRoute
  DemoStartApiRequestRoute: typeof DemoStartApiRequestRoute
  DemoStartServerFuncsRoute: typeof DemoStartServerFuncsRoute
}
export interface FileServerRoutesByFullPath {
  '/api/demo-names': typeof ApiDemoNamesServerRoute
}
export interface FileServerRoutesByTo {
  '/api/demo-names': typeof ApiDemoNamesServerRoute
}
export interface FileServerRoutesById {
  __root__: typeof rootServerRouteImport
  '/api/demo-names': typeof ApiDemoNamesServerRoute
}
export interface FileServerRouteTypes {
  fileServerRoutesByFullPath: FileServerRoutesByFullPath
  fullPaths: '/api/demo-names'
  fileServerRoutesByTo: FileServerRoutesByTo
  to: '/api/demo-names'
  id: '__root__' | '/api/demo-names'
  fileServerRoutesById: FileServerRoutesById
}
export interface RootServerRouteChildren {
  ApiDemoNamesServerRoute: typeof ApiDemoNamesServerRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/theme-demo': {
      id: '/theme-demo'
      path: '/theme-demo'
      fullPath: '/theme-demo'
      preLoaderRoute: typeof ThemeDemoRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard-test': {
      id: '/dashboard-test'
      path: '/dashboard-test'
      fullPath: '/dashboard-test'
      preLoaderRoute: typeof DashboardTestRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/auth': {
      id: '/auth'
      path: '/auth'
      fullPath: '/auth'
      preLoaderRoute: typeof AuthRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/demo/start/server-funcs': {
      id: '/demo/start/server-funcs'
      path: '/demo/start/server-funcs'
      fullPath: '/demo/start/server-funcs'
      preLoaderRoute: typeof DemoStartServerFuncsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/demo/start/api-request': {
      id: '/demo/start/api-request'
      path: '/demo/start/api-request'
      fullPath: '/demo/start/api-request'
      preLoaderRoute: typeof DemoStartApiRequestRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}
declare module '@tanstack/react-start/server' {
  interface ServerFileRoutesByPath {
    '/api/demo-names': {
      id: '/api/demo-names'
      path: '/api/demo-names'
      fullPath: '/api/demo-names'
      preLoaderRoute: typeof ApiDemoNamesServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AuthRoute: AuthRoute,
  DashboardRoute: DashboardRoute,
  DashboardTestRoute: DashboardTestRoute,
  ThemeDemoRoute: ThemeDemoRoute,
  DemoStartApiRequestRoute: DemoStartApiRequestRoute,
  DemoStartServerFuncsRoute: DemoStartServerFuncsRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
const rootServerRouteChildren: RootServerRouteChildren = {
  ApiDemoNamesServerRoute: ApiDemoNamesServerRoute,
}
export const serverRouteTree = rootServerRouteImport
  ._addFileChildren(rootServerRouteChildren)
  ._addFileTypes<FileServerRouteTypes>()
