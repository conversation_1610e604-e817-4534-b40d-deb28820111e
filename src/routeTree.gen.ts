/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as ThemeDemoRouteImport } from './routes/theme-demo'
import { Route as DashboardRouteImport } from './routes/dashboard'
import { Route as AuthRouteImport } from './routes/auth'
import { Route as IndexRouteImport } from './routes/index'

const ThemeDemoRoute = ThemeDemoRouteImport.update({
  id: '/theme-demo',
  path: '/theme-demo',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardRoute = DashboardRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthRoute = AuthRouteImport.update({
  id: '/auth',
  path: '/auth',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/auth': typeof AuthRoute
  '/dashboard': typeof DashboardRoute
  '/theme-demo': typeof ThemeDemoRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/auth': typeof AuthRoute
  '/dashboard': typeof DashboardRoute
  '/theme-demo': typeof ThemeDemoRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/auth': typeof AuthRoute
  '/dashboard': typeof DashboardRoute
  '/theme-demo': typeof ThemeDemoRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/auth' | '/dashboard' | '/theme-demo'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/auth' | '/dashboard' | '/theme-demo'
  id: '__root__' | '/' | '/auth' | '/dashboard' | '/theme-demo'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AuthRoute: typeof AuthRoute
  DashboardRoute: typeof DashboardRoute
  ThemeDemoRoute: typeof ThemeDemoRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/theme-demo': {
      id: '/theme-demo'
      path: '/theme-demo'
      fullPath: '/theme-demo'
      preLoaderRoute: typeof ThemeDemoRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/auth': {
      id: '/auth'
      path: '/auth'
      fullPath: '/auth'
      preLoaderRoute: typeof AuthRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AuthRoute: AuthRoute,
  DashboardRoute: DashboardRoute,
  ThemeDemoRoute: ThemeDemoRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
