import { createFileRoute, <PERSON> } from '@tanstack/react-router'
import React from 'react'

export const Route = createFileRoute('/')({
  component: HomePage,
})

function HomePage() {
  return (
    <div className="min-h-screen bg-white dark:bg-gray-950 flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          Welcome to Foundation for Music Education
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Mark of Excellence Competition Platform
        </p>
        <div className="space-x-4">
          <Link 
            to="/auth" 
            className="bg-brand-button hover:bg-brand-button-hover text-white px-6 py-3 rounded-md font-medium transition-colors"
          >
            Sign In / Sign Up
          </Link>
          <Link 
            to="/theme-demo" 
            className="bg-gray-200 dark:bg-gray-800 hover:bg-gray-300 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100 px-6 py-3 rounded-md font-medium transition-colors"
          >
            View Theme Demo
          </Link>
        </div>
      </div>
    </div>
  )
}
