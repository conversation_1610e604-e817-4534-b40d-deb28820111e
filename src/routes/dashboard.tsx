import { createFileRoute, redirect } from '@tanstack/react-router'
import { SidebarProvider, SidebarInset, SidebarTrigger } from '../components/ui/sidebar.tsx'
import { AppSidebar } from '../components/app-sidebar.tsx'
import { Separator } from '../components/ui/separator.tsx'

export const Route = createFileRoute('/dashboard')({
  beforeLoad: ({ context, location }) => {
    if (typeof window !== 'undefined') {
      const storedUser = localStorage.getItem('fme-user')
      if (!storedUser) {
        throw redirect({
          to: '/auth',
          search: {
            redirect: location.href,
          },
        })
      }
    }
  },
  component: DashboardPage,
})

function DashboardPage() {
  return (
    <SidebarProvider>
      <div className="flex min-h-screen w-full">
        <AppSidebar />
        <SidebarInset className="flex-1">
          <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 h-4" />
            <h1 className="text-xl font-semibold">FME Dashboard</h1>
          </header>
          <div className="p-6">
            <div className="space-y-6">
              <div className="grid gap-4 md:grid-cols-3">
                <div className="aspect-video rounded-xl bg-muted/50 p-4 flex items-center justify-center">
                  <p className="text-muted-foreground">Content Card 1</p>
                </div>
                <div className="aspect-video rounded-xl bg-muted/50 p-4 flex items-center justify-center">
                  <p className="text-muted-foreground">Content Card 2</p>
                </div>
                <div className="aspect-video rounded-xl bg-muted/50 p-4 flex items-center justify-center">
                  <p className="text-muted-foreground">Content Card 3</p>
                </div>
              </div>
              <div className="rounded-xl bg-card border p-6">
                <h2 className="text-lg font-semibold mb-2">Dashboard Content</h2>
                <p className="text-muted-foreground">This content should be positioned next to the sidebar, not behind it. The sidebar should be collapsible.</p>
              </div>
            </div>
          </div>
        </SidebarInset>
      </div>
    </SidebarProvider>
  )
}
