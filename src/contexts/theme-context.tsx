import React, { createContext, useContext, useEffect, useState } from 'react'
import { setTheme as setThemeUtil, getTheme, getEffectiveTheme } from '@/lib/utils'

type Theme = 'light' | 'dark' | 'system'

interface ThemeContextValue {
  theme: Theme
  effectiveTheme: 'light' | 'dark'
  setTheme: (theme: Theme) => void
}

const ThemeContext = createContext<ThemeContextValue | undefined>(undefined)

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setThemeState] = useState<Theme>('system')
  const [effectiveTheme, setEffectiveTheme] = useState<'light' | 'dark'>('light')

  useEffect(() => {
    // Initialize theme from localStorage or system preference
    const initialTheme = getTheme()
    setThemeState(initialTheme)
    setEffectiveTheme(getEffectiveTheme())

    // Apply initial theme
    setThemeUtil(initialTheme)

    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    const handleChange = () => {
      if (getTheme() === 'system') {
        setEffectiveTheme(getEffectiveTheme())
        setThemeUtil('system')
      }
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  const handleSetTheme = (newTheme: Theme) => {
    setThemeState(newTheme)
    setThemeUtil(newTheme)
    setEffectiveTheme(getEffectiveTheme())
  }

  return (
    <ThemeContext.Provider value={{ theme, effectiveTheme, setTheme: handleSetTheme }}>
      {children}
    </ThemeContext.Provider>
  )
}

export function useTheme() {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}
