import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { DashboardLayout } from '../dashboard-layout'

// Mock the dependencies
vi.mock('../dashboard-sidebar', () => ({
  DashboardSidebar: () => <div data-testid="dashboard-sidebar">Sidebar</div>
}))

vi.mock('../../contexts/theme-context', () => ({
  useTheme: vi.fn(() => ({
    theme: 'light',
    effectiveTheme: 'light',
    setTheme: vi.fn()
  }))
}))

vi.mock('../../ui/sidebar', () => ({
  SidebarProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  SidebarTrigger: ({ className }: { className?: string }) => (
    <button className={className} data-testid="sidebar-trigger">Toggle</button>
  ),
  useSidebar: vi.fn(() => ({
    state: 'expanded',
    open: true,
    setOpen: vi.fn(),
    openMobile: false,
    setOpenMobile: vi.fn(),
    isMobile: false,
    toggleSidebar: vi.fn()
  }))
}))

vi.mock('../../ui/separator', () => ({
  Separator: ({ orientation, className }: { orientation?: string; className?: string }) => (
    <div className={className} data-testid={`separator-${orientation}`} />
  )
}))

vi.mock('../../ui/button', () => ({
  Button: ({ children, className, ...props }: any) => (
    <button className={className} {...props}>{children}</button>
  )
}))

describe('DashboardLayout', () => {
  it('renders header with correct styling and dark blue background', () => {
    render(
      <DashboardLayout title="Test Dashboard" description="Test description">
        <div>Test content</div>
      </DashboardLayout>
    )

    // Find the header element
    const header = screen.getByRole('banner')

    // Check that the header has the correct classes including z-index and dark blue background
    expect(header).toHaveClass('relative', 'z-20')
    expect(header).toHaveClass('flex', 'h-16', 'shrink-0', 'items-center', 'gap-2', 'border-b', 'border-sidebar-border', 'bg-brand-nav', 'px-4')
  })

  it('renders sidebar trigger and separator', () => {
    render(
      <DashboardLayout title="Test Dashboard">
        <div>Test content</div>
      </DashboardLayout>
    )

    expect(screen.getByTestId('sidebar-trigger')).toBeInTheDocument()
    expect(screen.getByTestId('separator-vertical')).toBeInTheDocument()
  })

  it('renders title and description with white text for dark header', () => {
    render(
      <DashboardLayout title="Test Dashboard" description="Test description">
        <div>Test content</div>
      </DashboardLayout>
    )

    const title = screen.getByText('Test Dashboard')
    const description = screen.getByText('Test description')

    expect(title).toBeInTheDocument()
    expect(title).toHaveClass('text-white')

    expect(description).toBeInTheDocument()
    expect(description).toHaveClass('text-white/70')
  })

  it('renders children content', () => {
    render(
      <DashboardLayout>
        <div data-testid="test-content">Test content</div>
      </DashboardLayout>
    )

    expect(screen.getByTestId('test-content')).toBeInTheDocument()
  })

  it('renders sidebar component', () => {
    render(
      <DashboardLayout>
        <div>Test content</div>
      </DashboardLayout>
    )

    expect(screen.getByTestId('dashboard-sidebar')).toBeInTheDocument()
  })
})
