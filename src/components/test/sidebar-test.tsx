import React from 'react'
import { <PERSON>bar<PERSON>rovider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar'
import { AppSidebar } from '@/components/app-sidebar'
import { Separator } from '@/components/ui/separator'

export function SidebarTest() {
  return (
    <div className="min-h-screen bg-background">
      <SidebarProvider>
        <div className="flex min-h-screen w-full">
          <AppSidebar />
          <SidebarInset className="flex-1">
            <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mr-2 h-4" />
              <h1 className="text-xl font-semibold">Sidebar Layout Test</h1>
            </header>
            <div className="p-6">
              <div className="space-y-6">
                <div className="rounded-xl bg-card border p-6">
                  <h2 className="text-lg font-semibold mb-4">Sidebar Layout Test</h2>
                  <div className="space-y-4">
                    <p className="text-muted-foreground">
                      This content should be positioned to the right of the sidebar, not behind it.
                    </p>
                    <p className="text-muted-foreground">
                      When you click the sidebar toggle button (☰), the sidebar should collapse to show only icons,
                      and this content should move closer to the left edge but still leave space for the icon sidebar.
                    </p>
                    <p className="text-muted-foreground">
                      The sidebar should be:
                    </p>
                    <ul className="list-disc list-inside space-y-2 text-muted-foreground ml-4">
                      <li><strong>Expanded:</strong> 16rem (256px) wide - content should have 16rem left margin</li>
                      <li><strong>Collapsed:</strong> 3rem (48px) wide - content should have 3rem left margin</li>
                    </ul>
                  </div>
                </div>
                
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  <div className="aspect-video rounded-xl bg-muted/50 p-4 flex items-center justify-center">
                    <p className="text-muted-foreground text-center">Test Card 1</p>
                  </div>
                  <div className="aspect-video rounded-xl bg-muted/50 p-4 flex items-center justify-center">
                    <p className="text-muted-foreground text-center">Test Card 2</p>
                  </div>
                  <div className="aspect-video rounded-xl bg-muted/50 p-4 flex items-center justify-center">
                    <p className="text-muted-foreground text-center">Test Card 3</p>
                  </div>
                </div>
              </div>
            </div>
          </SidebarInset>
        </div>
      </SidebarProvider>
    </div>
  )
}
