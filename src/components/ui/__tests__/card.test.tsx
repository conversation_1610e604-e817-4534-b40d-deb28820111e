import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '../card'

describe('Card Component', () => {
  it('renders card with correct green theme classes', () => {
    render(
      <Card data-testid="test-card">
        <CardHeader>
          <CardTitle>Test Title</CardTitle>
          <CardDescription>Test Description</CardDescription>
        </CardHeader>
        <CardContent>
          <p>Test content</p>
        </CardContent>
        <CardFooter>
          <p>Test footer</p>
        </CardFooter>
      </Card>
    )

    const card = screen.getByTestId('test-card')

    // Check that the card has the correct classes with green border theme
    expect(card).toHaveClass('bg-card', 'text-card-foreground')
    expect(card).toHaveClass('rounded-xl', 'border-2', 'border-primary', 'shadow-lg')
  })

  it('renders card title with correct styling', () => {
    render(
      <Card>
        <CardHeader>
          <CardTitle>Test Title</CardTitle>
        </CardHeader>
      </Card>
    )

    const title = screen.getByText('Test Title')
    expect(title).toHaveClass('font-semibold', 'leading-none', 'tracking-tight')
  })

  it('renders card description with correct styling', () => {
    render(
      <Card>
        <CardHeader>
          <CardDescription>Test Description</CardDescription>
        </CardHeader>
      </Card>
    )

    const description = screen.getByText('Test Description')
    expect(description).toHaveClass('text-sm', 'text-muted-foreground')
  })

  it('renders card content with correct padding', () => {
    render(
      <Card>
        <CardContent data-testid="card-content">
          <p>Test content</p>
        </CardContent>
      </Card>
    )

    const content = screen.getByTestId('card-content')
    expect(content).toHaveClass('p-6', 'pt-0')
  })

  it('renders card footer with correct styling', () => {
    render(
      <Card>
        <CardFooter data-testid="card-footer">
          <p>Test footer</p>
        </CardFooter>
      </Card>
    )

    const footer = screen.getByTestId('card-footer')
    expect(footer).toHaveClass('flex', 'items-center', 'p-6', 'pt-0')
  })

  it('accepts custom className props', () => {
    render(
      <Card className="custom-card-class" data-testid="custom-card">
        <CardHeader className="custom-header-class">
          <CardTitle className="custom-title-class">Custom Title</CardTitle>
          <CardDescription className="custom-desc-class">Custom Description</CardDescription>
        </CardHeader>
        <CardContent className="custom-content-class">
          Custom content
        </CardContent>
        <CardFooter className="custom-footer-class">
          Custom footer
        </CardFooter>
      </Card>
    )

    expect(screen.getByTestId('custom-card')).toHaveClass('custom-card-class')
    expect(screen.getByText('Custom Title')).toHaveClass('custom-title-class')
    expect(screen.getByText('Custom Description')).toHaveClass('custom-desc-class')
    expect(screen.getByText('Custom content')).toHaveClass('custom-content-class')
    expect(screen.getByText('Custom footer')).toHaveClass('custom-footer-class')
  })
})
