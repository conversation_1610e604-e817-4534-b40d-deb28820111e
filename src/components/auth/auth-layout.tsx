import React, { useState } from 'react'
import { LoginForm } from './login-form'
import { RegisterForm } from './register-form'
import { ThemeToggle } from '@/components/ui/theme-toggle'

type AuthMode = 'login' | 'register'

export function AuthLayout() {
  const [mode, setMode] = useState<AuthMode>('login')

  const handleLogin = async (data: { email: string; password: string }) => {
    console.log('Login attempt:', data)
    // TODO: Implement actual login logic
    await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call
    alert('Login successful! (This is just a demo)')
  }

  const handleRegister = async (data: {
    firstName: string
    lastName: string
    email: string
    password: string
    confirmPassword: string
    acceptTerms: boolean
  }) => {
    console.log('Registration attempt:', data)
    // TODO: Implement actual registration logic
    await new Promise(resolve => setTimeout(resolve, 1500)) // Simulate API call
    alert('Registration successful! (This is just a demo)')
    setMode('login') // Switch to login after successful registration
  }

  return (
    <div className="min-h-screen bg-white dark:bg-gray-950">
      {/* Header */}
      <header className="bg-brand-nav">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16 items-center">
            <div className="flex items-center">
              <h1 className="text-white font-bold text-xl">FME Start</h1>
            </div>
            <ThemeToggle />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12">
        <div className="w-full max-w-md">
          {/* Background Card */}
          <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-8">
            {mode === 'login' ? (
              <LoginForm
                onSubmit={handleLogin}
                onSwitchToRegister={() => setMode('register')}
              />
            ) : (
              <RegisterForm
                onSubmit={handleRegister}
                onSwitchToLogin={() => setMode('login')}
              />
            )}
          </div>

          {/* Additional Info */}
          <div className="mt-8 text-center">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              By continuing, you acknowledge that you understand and agree to our{' '}
              <button className="text-brand-button hover:text-brand-button-hover transition-colors">
                Terms & Conditions
              </button>{' '}
              and{' '}
              <button className="text-brand-button hover:text-brand-button-hover transition-colors">
                Privacy Policy
              </button>
              .
            </p>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="mt-auto py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-sm text-gray-500 dark:text-gray-400">
            <p>&copy; 2024 FME Start. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
