import React, { useState } from 'react'
import { useForm } from '@tanstack/react-form'
import { cn } from '@/lib/utils'

interface RegisterFormData {
  firstName: string
  lastName: string
  email: string
  password: string
  confirmPassword: string
  acceptTerms: boolean
}

interface RegisterFormProps {
  onSubmit: (data: RegisterFormData) => Promise<void>
  onSwitchToLogin: () => void
}

export function RegisterForm({ onSubmit, onSwitchToLogin }: RegisterFormProps) {
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<RegisterFormData>({
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      confirmPassword: '',
      acceptTerms: false,
    },
    onSubmit: async ({ value }) => {
      setIsLoading(true)
      try {
        await onSubmit(value)
      } catch (error) {
        console.error('Registration failed:', error)
      } finally {
        setIsLoading(false)
      }
    },
  })

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          Create Account
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Sign up to get started
        </p>
      </div>

      <form
        onSubmit={(e) => {
          e.preventDefault()
          e.stopPropagation()
          form.handleSubmit()
        }}
        className="space-y-6"
      >
        {/* Name Fields */}
        <div className="grid grid-cols-2 gap-4">
          <form.Field
            name="firstName"
            validators={{
              onChange: ({ value }) => {
                if (!value) return 'First name is required'
                if (value.length < 2) return 'First name must be at least 2 characters'
                return undefined
              },
            }}
          >
            {(field) => (
              <div>
                <label 
                  htmlFor={field.name} 
                  className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2"
                >
                  First Name
                </label>
                <input
                  id={field.name}
                  name={field.name}
                  type="text"
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.handleChange(e.target.value)}
                  className={cn(
                    'w-full px-4 py-3 bg-white dark:bg-gray-800 border rounded-md text-gray-900 dark:text-gray-100',
                    'placeholder:text-gray-500 dark:placeholder:text-gray-400',
                    'focus:outline-none focus:ring-2 focus:ring-brand-button focus:ring-offset-2',
                    'transition-colors',
                    field.state.meta.errors.length > 0
                      ? 'border-red-300 dark:border-red-600'
                      : 'border-gray-300 dark:border-gray-600'
                  )}
                  placeholder="First name"
                  disabled={isLoading}
                />
                {field.state.meta.errors.map((error) => (
                  <p key={error} className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {error}
                  </p>
                ))}
              </div>
            )}
          </form.Field>

          <form.Field
            name="lastName"
            validators={{
              onChange: ({ value }) => {
                if (!value) return 'Last name is required'
                if (value.length < 2) return 'Last name must be at least 2 characters'
                return undefined
              },
            }}
          >
            {(field) => (
              <div>
                <label 
                  htmlFor={field.name} 
                  className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2"
                >
                  Last Name
                </label>
                <input
                  id={field.name}
                  name={field.name}
                  type="text"
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.handleChange(e.target.value)}
                  className={cn(
                    'w-full px-4 py-3 bg-white dark:bg-gray-800 border rounded-md text-gray-900 dark:text-gray-100',
                    'placeholder:text-gray-500 dark:placeholder:text-gray-400',
                    'focus:outline-none focus:ring-2 focus:ring-brand-button focus:ring-offset-2',
                    'transition-colors',
                    field.state.meta.errors.length > 0
                      ? 'border-red-300 dark:border-red-600'
                      : 'border-gray-300 dark:border-gray-600'
                  )}
                  placeholder="Last name"
                  disabled={isLoading}
                />
                {field.state.meta.errors.map((error) => (
                  <p key={error} className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {error}
                  </p>
                ))}
              </div>
            )}
          </form.Field>
        </div>

        {/* Email Field */}
        <form.Field
          name="email"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Email is required'
              const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
              if (!emailRegex.test(value)) return 'Please enter a valid email'
              return undefined
            },
          }}
        >
          {(field) => (
            <div>
              <label 
                htmlFor={field.name} 
                className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2"
              >
                Email Address
              </label>
              <input
                id={field.name}
                name={field.name}
                type="email"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                className={cn(
                  'w-full px-4 py-3 bg-white dark:bg-gray-800 border rounded-md text-gray-900 dark:text-gray-100',
                  'placeholder:text-gray-500 dark:placeholder:text-gray-400',
                  'focus:outline-none focus:ring-2 focus:ring-brand-button focus:ring-offset-2',
                  'transition-colors',
                  field.state.meta.errors.length > 0
                    ? 'border-red-300 dark:border-red-600'
                    : 'border-gray-300 dark:border-gray-600'
                )}
                placeholder="Enter your email"
                disabled={isLoading}
              />
              {field.state.meta.errors.map((error) => (
                <p key={error} className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {error}
                </p>
              ))}
            </div>
          )}
        </form.Field>

        {/* Password Field */}
        <form.Field
          name="password"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Password is required'
              if (value.length < 8) return 'Password must be at least 8 characters'
              if (!/[A-Z]/.test(value)) return 'Password must contain at least one uppercase letter'
              if (!/[a-z]/.test(value)) return 'Password must contain at least one lowercase letter'
              if (!/\d/.test(value)) return 'Password must contain at least one number'
              return undefined
            },
          }}
        >
          {(field) => (
            <div>
              <label 
                htmlFor={field.name} 
                className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2"
              >
                Password
              </label>
              <input
                id={field.name}
                name={field.name}
                type="password"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                className={cn(
                  'w-full px-4 py-3 bg-white dark:bg-gray-800 border rounded-md text-gray-900 dark:text-gray-100',
                  'placeholder:text-gray-500 dark:placeholder:text-gray-400',
                  'focus:outline-none focus:ring-2 focus:ring-brand-button focus:ring-offset-2',
                  'transition-colors',
                  field.state.meta.errors.length > 0
                    ? 'border-red-300 dark:border-red-600'
                    : 'border-gray-300 dark:border-gray-600'
                )}
                placeholder="Create a password"
                disabled={isLoading}
              />
              {field.state.meta.errors.map((error) => (
                <p key={error} className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {error}
                </p>
              ))}
            </div>
          )}
        </form.Field>

        {/* Confirm Password Field */}
        <form.Field
          name="confirmPassword"
          validators={{
            onChangeListenTo: ['password'],
            onChange: ({ value, fieldApi }) => {
              if (!value) return 'Please confirm your password'
              const password = fieldApi.form.getFieldValue('password')
              if (value !== password) return 'Passwords do not match'
              return undefined
            },
          }}
        >
          {(field) => (
            <div>
              <label 
                htmlFor={field.name} 
                className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2"
              >
                Confirm Password
              </label>
              <input
                id={field.name}
                name={field.name}
                type="password"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                className={cn(
                  'w-full px-4 py-3 bg-white dark:bg-gray-800 border rounded-md text-gray-900 dark:text-gray-100',
                  'placeholder:text-gray-500 dark:placeholder:text-gray-400',
                  'focus:outline-none focus:ring-2 focus:ring-brand-button focus:ring-offset-2',
                  'transition-colors',
                  field.state.meta.errors.length > 0
                    ? 'border-red-300 dark:border-red-600'
                    : 'border-gray-300 dark:border-gray-600'
                )}
                placeholder="Confirm your password"
                disabled={isLoading}
              />
              {field.state.meta.errors.map((error) => (
                <p key={error} className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {error}
                </p>
              ))}
            </div>
          )}
        </form.Field>

        {/* Terms Checkbox */}
        <form.Field
          name="acceptTerms"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'You must accept the terms and conditions'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="flex items-start">
              <input
                id={field.name}
                name={field.name}
                type="checkbox"
                checked={field.state.value}
                onChange={(e) => field.handleChange(e.target.checked)}
                className="mt-1 h-4 w-4 text-brand-button focus:ring-brand-button border-gray-300 rounded"
                disabled={isLoading}
              />
              <label htmlFor={field.name} className="ml-3 text-sm text-gray-900 dark:text-gray-100">
                I agree to the{' '}
                <button type="button" className="text-brand-button hover:text-brand-button-hover">
                  Terms of Service
                </button>{' '}
                and{' '}
                <button type="button" className="text-brand-button hover:text-brand-button-hover">
                  Privacy Policy
                </button>
              </label>
              {field.state.meta.errors.map((error) => (
                <p key={error} className="mt-1 text-sm text-red-600 dark:text-red-400 block w-full">
                  {error}
                </p>
              ))}
            </div>
          )}
        </form.Field>

        {/* Submit Button */}
        <form.Subscribe
          selector={(state) => [state.canSubmit, state.isSubmitting]}
        >
          {([canSubmit, isSubmitting]) => (
            <button
              type="submit"
              disabled={!canSubmit || isLoading}
              className={cn(
                'w-full bg-brand-button hover:bg-brand-button-hover text-white font-medium py-3 px-4 rounded-md',
                'transition-colors focus:outline-none focus:ring-2 focus:ring-brand-button focus:ring-offset-2',
                'disabled:opacity-50 disabled:cursor-not-allowed'
              )}
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Creating Account...
                </div>
              ) : (
                'Create Account'
              )}
            </button>
          )}
        </form.Subscribe>
      </form>

      {/* Switch to Login */}
      <div className="mt-6 text-center">
        <p className="text-gray-600 dark:text-gray-400">
          Already have an account?{' '}
          <button
            type="button"
            onClick={onSwitchToLogin}
            className="text-brand-button hover:text-brand-button-hover font-medium transition-colors"
          >
            Sign in
          </button>
        </p>
      </div>
    </div>
  )
}
