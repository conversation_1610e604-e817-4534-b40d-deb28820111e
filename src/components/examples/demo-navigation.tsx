import React from 'react'
import { <PERSON> } from '@tanstack/react-router'
import { ThemeToggle } from '@/components/ui/theme-toggle'
import { cn } from '@/lib/utils'

export function DemoNavigation() {
  return (
    <nav className="bg-brand-nav border-b border-brand-nav-hover">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo and main nav */}
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <Link to="/" className="text-white font-bold text-xl hover:text-white/90">
                FME Start
              </Link>
            </div>
            <div className="hidden md:ml-6 md:flex md:space-x-8">
              <NavLink to="/theme-demo">Theme Demo</NavLink>
              <NavLink to="/demo/start/server-funcs">Server Functions</NavLink>
              <NavLink to="/demo/start/api-request">API Request</NavLink>
            </div>
          </div>

          {/* Right side nav */}
          <div className="flex items-center space-x-4">
            <ThemeToggle />
            <Link
              to="/"
              className="bg-brand-button hover:bg-brand-button-hover text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
            >
              Back to Login
            </Link>
          </div>
        </div>
      </div>

      {/* Mobile menu (collapsed) */}
      <div className="md:hidden">
        <div className="px-2 pt-2 pb-3 space-y-1 bg-brand-nav-hover">
          <MobileNavLink to="/theme-demo">Theme Demo</MobileNavLink>
          <MobileNavLink to="/demo/start/server-funcs">Server Functions</MobileNavLink>
          <MobileNavLink to="/demo/start/api-request">API Request</MobileNavLink>
        </div>
      </div>
    </nav>
  )
}

interface NavLinkProps {
  to: string
  children: React.ReactNode
}

function NavLink({ to, children }: NavLinkProps) {
  return (
    <Link
      to={to}
      className="inline-flex items-center px-1 pt-1 text-sm font-medium transition-colors border-b-2 border-transparent hover:border-white/30 hover:text-white/90 text-white/70 hover:text-white [&.active]:text-white [&.active]:border-white"
      activeOptions={{ exact: to === '/' }}
    >
      {children}
    </Link>
  )
}

function MobileNavLink({ to, children }: NavLinkProps) {
  return (
    <Link
      to={to}
      className="block px-3 py-2 text-base font-medium rounded-md transition-colors text-white/70 hover:bg-brand-nav hover:text-white [&.active]:bg-brand-nav [&.active]:text-white"
      activeOptions={{ exact: to === '/' }}
    >
      {children}
    </Link>
  )
}
