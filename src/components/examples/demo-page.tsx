import React from 'react'
import { cn } from '@/lib/utils'

export function DemoPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-gray-950">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Welcome to FME Start
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 mb-8">
            A modern theme system built with shadcn/ui and your brand colors
          </p>
          <div className="flex justify-center gap-4">
            <button className="bg-brand-button hover:bg-brand-button-hover text-white px-6 py-3 rounded-md font-medium transition-colors">
              Primary Action
            </button>
            <button className="bg-gray-200 dark:bg-gray-800 hover:bg-gray-300 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100 px-6 py-3 rounded-md font-medium transition-colors">
              Secondary Action
            </button>
          </div>
        </div>

        {/* Feature Cards */}
        <div className="grid md:grid-cols-3 gap-6 mb-12">
          <FeatureCard
            title="Brand Navigation"
            description="Header navigation using your brand navy color (#032741)"
            color="nav"
          />
          <FeatureCard
            title="Primary Actions"
            description="Buttons and CTAs using your brand green (#008544)"
            color="button"
          />
          <FeatureCard
            title="Theme System"
            description="Seamless light/dark mode with proper contrast ratios"
            color="theme"
          />
        </div>

        {/* Color Palette Demo */}
        <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-6 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6">Color Palette</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <ColorSwatch name="Brand Nav" color="bg-brand-nav" hex="#032741" />
            <ColorSwatch name="Brand Button" color="bg-brand-button" hex="#008544" />
            <ColorSwatch name="Primary" color="bg-blue-600" desc="Theme primary" />
            <ColorSwatch name="Secondary" color="bg-gray-600" desc="Theme secondary" />
            <ColorSwatch name="Accent" color="bg-purple-600" desc="Theme accent" />
            <ColorSwatch name="Muted" color="bg-gray-100 dark:bg-gray-800" desc="Theme muted" />
            <ColorSwatch name="Card" color="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700" desc="Card background" />
            <ColorSwatch name="Background" color="bg-gray-50 dark:bg-gray-950" desc="Page background" />
          </div>
        </div>

        {/* Form Demo */}
        <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6">Form Components</h2>
          <div className="space-y-4 max-w-md">
            <div>
              <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                Email
              </label>
              <input
                type="email"
                className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                placeholder="Enter your email"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                Message
              </label>
              <textarea
                className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                rows={3}
                placeholder="Your message..."
              />
            </div>
            <button className="bg-brand-button hover:bg-brand-button-hover text-white px-4 py-2 rounded-md font-medium transition-colors">
              Send Message
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

interface FeatureCardProps {
  title: string
  description: string
  color: 'nav' | 'button' | 'theme'
}

function FeatureCard({ title, description, color }: FeatureCardProps) {
  const colorClasses = {
    nav: 'border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-950',
    button: 'border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-950',
    theme: 'border-purple-200 dark:border-purple-800 bg-purple-50 dark:bg-purple-950'
  }

  return (
    <div className={cn('p-6 rounded-lg border-2 transition-colors', colorClasses[color])}>
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">{title}</h3>
      <p className="text-gray-600 dark:text-gray-400">{description}</p>
    </div>
  )
}

interface ColorSwatchProps {
  name: string
  color: string
  hex?: string
  desc?: string
}

function ColorSwatch({ name, color, hex, desc }: ColorSwatchProps) {
  return (
    <div className="text-center">
      <div className={cn('w-full h-16 rounded-md mb-2', color)} />
      <div className="text-sm font-medium text-gray-900 dark:text-gray-100">{name}</div>
      {hex && <div className="text-xs text-gray-500 dark:text-gray-400">{hex}</div>}
      {desc && <div className="text-xs text-gray-500 dark:text-gray-400">{desc}</div>}
    </div>
  )
}
