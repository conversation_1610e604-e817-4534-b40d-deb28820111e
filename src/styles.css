@import "tailwindcss";

@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

/* Base styles */
body {
  @apply m-0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

/* Theme Variables */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%; /* White background for cards in light mode */
    --card-foreground: 222.2 84% 4.9%; /* Dark text on white cards */
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Primary colors based on your brand button color (#008544) */
    --primary: 145 100% 26%;  /* #008544 converted to HSL */
    --primary-foreground: 0 0% 100%;

    /* Secondary colors based on your nav color (#032741) */
    --secondary: 202 88% 13%;  /* #032741 converted to HSL */
    --secondary-foreground: 0 0% 100%;

    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 145 100% 26%; /* Same as primary for consistency */
    --radius: 0.5rem;

    /* Brand-specific variables */
    --brand-nav: 202 88% 13%;
    --brand-nav-hover: 202 88% 20%;
    --brand-button: 145 100% 26%;
    --brand-button-hover: 145 100% 32%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%; /* Dark background for cards in dark mode */
    --card-foreground: 210 40% 98%; /* Light text on dark cards */
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    /* Primary - slightly lighter for better contrast in dark mode */
    --primary: 145 100% 35%;  /* Lighter version of #008544 */
    --primary-foreground: 222.2 84% 4.9%;

    /* Secondary - lighter nav color for dark mode */
    --secondary: 202 88% 25%;  /* Lighter version of #032741 */
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 145 100% 35%;

    /* Brand-specific dark mode variables - keep header same dark blue */
    --brand-nav: 202 88% 13%; /* Same dark blue as light mode */
    --brand-nav-hover: 202 88% 20%; /* Same hover as light mode */
    --brand-button: 145 100% 35%;
    --brand-button-hover: 145 100% 42%;
  }
}

/* Brand utility classes */
@layer utilities {
  .bg-brand-nav {
    background-color: hsl(var(--brand-nav));
  }

  .bg-brand-nav-hover {
    background-color: hsl(var(--brand-nav-hover));
  }

  .bg-brand-button {
    background-color: hsl(var(--brand-button));
  }

  .bg-brand-button-hover {
    background-color: hsl(var(--brand-button-hover));
  }

  .text-brand-nav {
    color: hsl(var(--brand-nav));
  }

  .text-brand-button {
    color: hsl(var(--brand-button));
  }

  .border-brand-nav {
    border-color: hsl(var(--brand-nav));
  }

  .border-brand-button {
    border-color: hsl(var(--brand-button));
  }
}

/* Base component styles */
@layer base {
  body {
    @apply bg-white text-gray-950;
  }

  .dark body {
    @apply bg-gray-950 text-gray-50;
  }
}

/* Enhanced focus styles for accessibility */
@layer utilities {
  .focus-visible-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2;
  }

  .focus-visible-ring-brand {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2;
    --tw-ring-color: hsl(var(--brand-button));
  }
}

/* Smooth transitions for theme switching */
@layer base {
  html {
    transition: color-scheme 0.2s ease-in-out;
  }

  * {
    transition: background-color 0.2s ease-in-out, 
                border-color 0.2s ease-in-out,
                color 0.2s ease-in-out;
  }
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.129 0.042 264.695);
  --card: oklch(1 0 0); /* White background for cards in light mode */
  --card-foreground: oklch(0.129 0.042 264.695); /* Dark text on white cards */
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.129 0.042 264.695);
  --primary: oklch(0.208 0.042 265.755);
  --primary-foreground: oklch(0.984 0.003 247.858);
  --secondary: oklch(0.968 0.007 247.896);
  --secondary-foreground: oklch(0.208 0.042 265.755);
  --muted: oklch(0.968 0.007 247.896);
  --muted-foreground: oklch(0.554 0.046 257.417);
  --accent: oklch(0.968 0.007 247.896);
  --accent-foreground: oklch(0.208 0.042 265.755);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.929 0.013 255.508);
  --input: oklch(0.929 0.013 255.508);
  --ring: oklch(0.704 0.04 256.788);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.15 0.08 240); /* Dark blue sidebar in light mode */
  --sidebar-foreground: oklch(0.984 0.003 247.858); /* White text */
  --sidebar-primary: oklch(0.55 0.15 145); /* Green primary */
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858); /* White text */
  --sidebar-accent: oklch(0.25 0.08 240); /* Slightly lighter blue for hover */
  --sidebar-accent-foreground: oklch(0.984 0.003 247.858); /* White text */
  --sidebar-border: oklch(0.12 0.08 240); /* Darker blue border */
  --sidebar-ring: oklch(0.55 0.15 145); /* Green ring */
}

.dark {
  --background: oklch(0.129 0.042 264.695);
  --foreground: oklch(0.984 0.003 247.858);
  --card: oklch(0.208 0.042 265.755); /* Dark background for cards in dark mode */
  --card-foreground: oklch(0.984 0.003 247.858); /* Light text on dark cards */
  --popover: oklch(0.208 0.042 265.755);
  --popover-foreground: oklch(0.984 0.003 247.858);
  --primary: oklch(0.929 0.013 255.508);
  --primary-foreground: oklch(0.208 0.042 265.755);
  --secondary: oklch(0.279 0.041 260.031);
  --secondary-foreground: oklch(0.984 0.003 247.858);
  --muted: oklch(0.279 0.041 260.031);
  --muted-foreground: oklch(0.704 0.04 256.788);
  --accent: oklch(0.279 0.041 260.031);
  --accent-foreground: oklch(0.984 0.003 247.858);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.551 0.027 264.364);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.18 0.08 240); /* Slightly lighter blue for dark mode */
  --sidebar-foreground: oklch(0.984 0.003 247.858); /* White text */
  --sidebar-primary: oklch(0.65 0.15 145); /* Lighter green for dark mode */
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858); /* White text */
  --sidebar-accent: oklch(0.3 0.08 240); /* Lighter blue for hover in dark mode */
  --sidebar-accent-foreground: oklch(0.984 0.003 247.858); /* White text */
  --sidebar-border: oklch(0.1 0.08 240); /* Darker border for dark mode */
  --sidebar-ring: oklch(0.65 0.15 145); /* Lighter green ring */
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Sidebar layout fix for desktop-only fixed sidebar */
@layer components {
  /* On desktop, always show sidebar and add margin to main content */
  @media (min-width: 768px) {
    /* Force sidebar to always be visible on desktop */
    .peer[data-variant="sidebar"][data-collapsible="offcanvas"] {
      position: relative !important;
      left: 0 !important;
    }

    /* Ensure main content always has margin on desktop */
    .peer[data-variant="sidebar"] ~ main {
      margin-left: var(--sidebar-width, 16rem);
    }
  }

  /* On mobile, sidebar is handled by Sheet component, no margin needed */
  @media (max-width: 767px) {
    .peer[data-variant="sidebar"] ~ main {
      margin-left: 0;
    }
  }
}


